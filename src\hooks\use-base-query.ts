import { useQuery, UseQueryOptions } from '@tanstack/react-query';
import { fetcher } from '../lib/base-api';

// Example: useBaseQuery for GET requests
export function useBaseQuery<T>(
  key: string | unknown[],
  url: string,
  options?: Omit<UseQueryOptions<T, Error>, 'queryKey' | 'queryFn'>
) {
  return useQuery<T, Error>({
    queryKey: Array.isArray(key) ? key : [key],
    queryFn: () => fetcher<T>(url),
    ...options,
  });
}
