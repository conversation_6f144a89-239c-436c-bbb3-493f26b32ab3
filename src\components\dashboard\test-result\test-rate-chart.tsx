import React from 'react';
import Chart from 'react-apexcharts';

const TestRateChart = () => {
  const dates = [
    '01-30',
    '01-31',
    '02-01',
    '02-02',
    '02-03',
    '02-04',
    '02-05',
    '02-06',
    '02-07',
    '02-08',
    '02-09',
    '02-10',
    '02-11',
    '02-12',
    '02-13',
    '02-14',
    '02-15',
    '02-16',
  ];

  const values = [0, 7.7, 15.4, 23.1, 30.8, 38.5, 46.2, 53.8, 61.5, 69.2, 69.2, 76.9, 76.9, 84.6, 84.6, 92.3, 100];

  const series = [
    {
      name: '累積目標',
      data: values,
    },
  ];

  const options: ApexCharts.ApexOptions = {
    chart: {
      type: 'line',
      height: 400,
      toolbar: { show: false },
    },
    title: {
      text: 'テスト実施率',
      align: 'center',
    },
    stroke: {
      curve: 'straight',
      width: 3,
    },
    markers: {
      size: 5,
      colors: ['#666'],
      strokeWidth: 2,
      hover: { size: 7 },
    },
    dataLabels: {
      enabled: true,
      formatter: (val: number) => `${val.toFixed(1)}%`,
      style: {
        colors: ['#666'],
      },
    },
    xaxis: {
      categories: dates,
      labels: {
        rotate: -45,
      },
    },
    yaxis: {
      min: 0,
      max: 110,
      tickAmount: 11,
      labels: {
        formatter: (val: number) => `${val}%`,
      },
    },
    grid: {
      borderColor: '#e0e0e0',
    },
    legend: {
      position: 'top',
      horizontalAlign: 'right',
    },
    colors: ['#666'],
  };

  return <Chart options={options} series={series} type="line" height={400} />;
};

export default TestRateChart;
