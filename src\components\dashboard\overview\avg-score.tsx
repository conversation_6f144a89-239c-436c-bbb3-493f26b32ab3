import * as React from 'react';
import type { SxProps } from '@mui/material/styles';
import { Command as CommandIcon } from '@phosphor-icons/react/dist/ssr/Command';

import { OverviewCard } from './overview-card';

export interface AvgQualityScoreProps {
  sx?: SxProps;
  value: string;
  view?: 'month' | 'week';
  diff?: number;
  trend?: 'up' | 'down';
}

export function AvgQualityScore({ sx, value, view = 'month', diff, trend }: AvgQualityScoreProps): React.JSX.Element {
  return (
    <OverviewCard
      sx={sx}
      value={value}
      label="平均品質スコア"
      icon={<CommandIcon fontSize="var(--icon-fontSize-lg)" />}
      avatarColor="var(--mui-palette-success-main)"
      diff={diff}
      trend={trend}
      view={view}
    />
  );
}
