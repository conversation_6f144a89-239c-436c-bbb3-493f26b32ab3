import axios, { AxiosRequestConfig } from 'axios';

const BASE_URL = process.env.NEXT_PUBLIC_DOMAIN_BE || '';

function joinUrl(base: string, path: string) {
  if (!base.endsWith('/') && !path.startsWith('/')) return base + '/' + path;
  if (base.endsWith('/') && path.startsWith('/')) return base + path.slice(1);
  return base + path;
}

/**
 * Generic API fetcher with error handling and JSON parsing using axios.
 * @template T - Expected response type
 * @param input - Request URL or Request object
 * @param init - Axios request config
 * @returns Parsed JSON response
 */
export async function fetcher<T>(
  input: string,
  init?: AxiosRequestConfig
): Promise<T> {
  try {
    let url: string;
    if (input.startsWith('http')) {
      url = input;
    } else if (input.startsWith('/api/proxy/')) {
      // Call local Next.js proxy endpoint directly
      url = input;
    } else {
      url = joinUrl(BASE_URL, input);
    }
    const response = await axios(url, init);
    return response.data as T;
  } catch (error: any) {
    const errorMessage =
      error.response?.data?.message || error.message || 'API error';
    throw new Error(errorMessage);
  }
}
