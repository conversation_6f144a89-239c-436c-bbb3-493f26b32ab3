import React from 'react';
import { Card } from '@mui/material';
import ReactApexChart from 'react-apexcharts';

export default function JiraStatusChart() {
  const series = [
    {
      name: 'クリティカル',
      data: [2, 5, 8, 0, 2], // mockup data
    },
    {
      name: '重大',
      data: [0, 70, 0, 0, 0], // mockup data
    },
    {
      name: '軽微',
      data: [0, 25, 0, 3, 0], // mockup data
    },
  ];

  const options: ApexCharts.ApexOptions = {
    chart: {
      type: 'bar',
      stacked: true,
    },
    title: {
      text: 'JIRA課題の現状',
      align: 'center',
    },
    xaxis: {
      categories: ['完了', '解決済み', '新規', '割り当て済み', '進行中'],
    },
    legend: {
      position: 'right',
    },
    colors: ['#d32f2f', '#81c784', '#ffeb3b'], // red, green, yellow
    plotOptions: {
      bar: {
        horizontal: false,
        columnWidth: '50%',
      },
    },
  };

  return (
    <Card sx={{ p: 2 }}>
      <ReactApexChart options={options} series={series} type="bar" height={400} />;
    </Card>
  );
}
