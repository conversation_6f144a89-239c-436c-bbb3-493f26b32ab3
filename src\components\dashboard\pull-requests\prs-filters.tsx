'use client';

import * as React from 'react';
import { Box, Card, InputAdornment, MenuItem, OutlinedInput, Select, TextField } from '@mui/material';
import { MagnifyingGlass as MagnifyingGlassIcon } from '@phosphor-icons/react/dist/ssr/MagnifyingGlass';

export interface PRsFiltersProps {
  search: string;
  onSearchChange: (value: string) => void;
  statusFilter: string;
  onStatusFilterChange: (value: string) => void;
  authorFilter: string;
  onAuthorFilterChange: (value: string) => void;
}

const statuses = [
  { label: 'すべてのステータス', value: '' },
  { label: 'Open', value: 'open' },
  { label: 'Closed', value: 'closed' },
  { label: 'Merged', value: 'merged' },
];

export function PRsFilters({
  search,
  onSearchChange,
  statusFilter,
  onStatusFilterChange,
  authorFilter,
  onAuthorFilterChange,
}: PRsFiltersProps): React.JSX.Element {
  return (
    <Card sx={{ p: 2 }}>
      <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
        <OutlinedInput
          value={search}
          onChange={(e) => onSearchChange(e.target.value)}
          fullWidth
          placeholder="タイトルを検索"
          startAdornment={
            <InputAdornment position="start">
              <MagnifyingGlassIcon fontSize="var(--icon-fontSize-md)" />
            </InputAdornment>
          }
          sx={{ maxWidth: '500px', height: 40 }}
        />
        <Select
          value={statusFilter}
          displayEmpty
          onChange={(e) => onStatusFilterChange(e.target.value)}
          size="small"
          sx={{ minWidth: 140, height: 40, '.MuiSelect-select': { display: 'flex', alignItems: 'center', height: '100%' } }}
        >
          {statuses.map((status) => (
            <MenuItem key={status.value} value={status.value}>
              {status.label}
            </MenuItem>
          ))}
        </Select>
      </Box>
    </Card>
  );
}
