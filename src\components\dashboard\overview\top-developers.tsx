import React from 'react';
import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import CardHeader from '@mui/material/CardHeader';
import Divider from '@mui/material/Divider';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemText from '@mui/material/ListItemText';
import type { SxProps } from '@mui/material/styles';

interface Developer {
  id: number;
  name: string;
  prs: number;
  comments: number;
  score: number | string;
}

export interface TopDevelopersProps {
  sx?: SxProps;
  view?: 'month' | 'week';
  data?: Developer[];
}

export function TopDevelopers({ sx, view = 'month', data }: TopDevelopersProps): React.JSX.Element {
  const developers = data ?? [];

  return (
    <Card sx={sx}>
      <CardHeader title="開発者ランキング" sx={{ textAlign: 'center', fontWeight: 700, fontSize: 24 }} />
      <Divider />
      <List>
        {developers.map((dev, idx) => (
          <ListItem key={dev.id} divider={idx < developers.length - 1} sx={{ alignItems: 'center' }}>
            <Box sx={{ minWidth: 32, textAlign: 'center', color: '#5B6AC6', fontWeight: 700, fontSize: 20, mr: 2 }}>
              #{idx + 1}
            </Box>
            <ListItemText
              primary={dev.name}
              primaryTypographyProps={{ fontWeight: 600, fontSize: 17 }}
              secondary={`${dev.prs} PRs, ${dev.comments} comments`}
              secondaryTypographyProps={{ fontSize: 14, color: 'text.secondary' }}
            />
            <Box
              sx={{
                ml: 'auto',
                background: '#7B6CF6',
                color: '#fff',
                borderRadius: 2,
                px: 2,
                py: 0.5,
                fontWeight: 700,
                fontSize: 18,
              }}
            >
              {typeof dev.score === 'string' ? parseFloat(dev.score).toFixed(1) : dev.score.toFixed(1)}
            </Box>
          </ListItem>
        ))}
      </List>
    </Card>
  );
}
