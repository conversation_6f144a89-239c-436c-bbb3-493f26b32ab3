import * as React from 'react';
import {
  Box,
  Grid,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from '@mui/material';

// Mock data cho Test Progress
const progressData = {
  progress: '100.0%',
  block: '0.0%',
  notExecuted: '0.0%',
  passRate: '100.00%',
  failRate: '0.00%',
};

// Mock data cho Test Results
const resultRows = [
  { category: '全体統計', total: 3681, pass: 3499, fail: 0, na: 182, block: 0, noRun: 0 },
  { category: 'メイン', total: 400, pass: 400, fail: 0, na: 0, block: 0, noRun: 0 },
  { category: '検索', total: 87, pass: 87, fail: 0, na: 0, block: 0, noRun: 0 },
  { category: 'コンテンツ', total: 361, pass: 328, fail: 0, na: 33, block: 0, noRun: 0 },
  { category: 'ライブラリ', total: 114, pass: 109, fail: 0, na: 5, block: 0, noRun: 0 },
  { category: 'マイページ', total: 1328, pass: 1235, fail: 0, na: 93, block: 0, noRun: 0 },
  { category: 'クラス', total: 1391, pass: 1340, fail: 0, na: 51, block: 0, noRun: 0 },
];

export default function TestDashboard() {
  return (
    <Paper sx={{ p: 2 }}>
      <Grid container>
        <Grid xs={4} textAlign={'center'} sx={{ borderRight: '1px solid #ccc' }}>
          <Typography variant="subtitle1" fontWeight="bold">
            テスト進捗状況 (test progress)
          </Typography>
        </Grid>
        <Grid item xs={8} textAlign={'center'}>
          <Typography variant="subtitle1" fontWeight="bold">
            テスト項目 (test item)
          </Typography>
        </Grid>
      </Grid>

      <Grid container>
        <Grid item xs={4} sx={{ borderRight: '1px solid #ccc', borderTop: '1px solid #ccc' }}>
          <Table size="small" style={{ height: '100%' }}>
            <TableBody>
              <TableRow>
                <TableCell align="center">進捗率 (%)</TableCell>
                <TableCell align="center">ブロック (%)</TableCell>
                <TableCell align="center">未実行率 (%)</TableCell>
              </TableRow>
              <TableRow>
                <TableCell align="center">{progressData.progress}</TableCell>
                <TableCell align="center">{progressData.block}</TableCell>
                <TableCell align="center">{progressData.notExecuted}</TableCell>
              </TableRow>
              <TableRow style={{ flexGrow: 1 }}>
                <TableCell align="center" colSpan={2} sx={{ color: 'blue' }}>
                  合格率 (%) {progressData.passRate}
                </TableCell>
                <TableCell align="center" sx={{ color: 'red' }}>
                  不合格率 (%) {progressData.failRate}
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </Grid>

        <Grid item xs={8} sx={{ borderTop: '1px solid #ccc' }}>
          <TableContainer>
            <Table size="small">
              <TableHead sx={{ backgroundColor: '#f0f0f0' }}>
                <TableRow>
                  <TableCell>FO</TableCell>
                  <TableCell align="right">合計</TableCell>
                  <TableCell align="right">合格</TableCell>
                  <TableCell align="right">失敗</TableCell>
                  <TableCell align="right">N/A</TableCell>
                  <TableCell align="right">ブロック</TableCell>
                  <TableCell align="right">No Run</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {resultRows.map((row) => (
                  <TableRow key={row.category}>
                    <TableCell>{row.category}</TableCell>
                    <TableCell align="right">{row.total}</TableCell>
                    <TableCell align="right" style={{ color: 'blue' }}>
                      {row.pass}
                    </TableCell>
                    <TableCell align="right" style={{ color: 'red' }}>
                      {row.fail}
                    </TableCell>
                    <TableCell align="right">{row.na}</TableCell>
                    <TableCell align="right">{row.block}</TableCell>
                    <TableCell align="right">{row.noRun}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Grid>
      </Grid>
    </Paper>
  );
}
