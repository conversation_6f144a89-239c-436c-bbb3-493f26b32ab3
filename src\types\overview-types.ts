export type BugSeverity = 'CRITICAL' | 'MAJOR' | 'MINOR' | 'NITPICK' | 'QUESTION';

export interface Bug {
  id: string;
  title: string;
  status: string;
  severity: BugSeverity;
  createdAt: string;
  resolvedAt?: string;
}

export interface Developer {
  id: string;
  name: string;
  prs: number;
  comments: number;
  avgReviewTime: number;
  score: number;
}

export interface BugTypeStatus {
  data: number[]; // counts per bug type
  labels: [
    'CODE_QUALITY',
    'BUSINESS_LOGIC',
    'PERFORMANCE',
    'SECURITY',
    'TESTING',
    'ARCHITECTURE',
    'DOCUMENTATION',
    'PRAISE',
  ]; // bug type names
  colors: string[]; // color hex codes for each type
}

export interface CodeReviewImpact {
  categories: string[]; // e.g. ['Resolved Comment', 'New Comment', ...]
  series: Array<{ name: string; data: number[]; color: string }>;
}

export interface CodeQualityChartData {
  categories: string[]; // x-axis labels (e.g. days or weeks)
  qualityScore: number[]; // average quality score per period
  bugCount: number[]; // bug count per period
}

export interface OverviewData {
  totalPRs: number;
  avgQualityScore: number;
  criticalIssues: number;
  totalDevelopers: number;
  bugs: Bug[];
  topDevelopers: Developer[];
  bugTypeStatus: BugTypeStatus;
  codeReviewImpact: CodeReviewImpact;
  codeQualityChartData: CodeQualityChartData;
}
