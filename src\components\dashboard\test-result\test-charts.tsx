import React from 'react';
import { Card, Typography } from '@mui/material';
import { ApexOptions } from 'apexcharts';
import Chart from 'react-apexcharts';

const TestCharts = () => {
  // Bar chart
  const barOptions: ApexOptions = {
    chart: {
      type: 'bar', // ✅ type hợp lệ
      toolbar: { show: false },
    },
    plotOptions: {
      bar: {
        horizontal: false,
        columnWidth: '50%',
      },
    },
    xaxis: {
      categories: ['FO_Main', 'FO_Search', 'FO_Content', 'FO_Library', 'FO_My Page', 'FO_Class'],
    },
    legend: {
      position: 'right',
    },
    colors: ['#90ed7d', '#f45b5b', '#2f7ed8'],
  };

  const barSeries = [
    { name: '未実行', data: [0, 0, 0, 0, 0, 0] },
    { name: 'ブロック', data: [0, 0, 0, 0, 0, 0] },
    { name: '合格', data: [0, 0, 32, 5, 93, 50] },
  ];

  // Pie chart
  const pieOptions: ApexOptions = {
    chart: {
      type: 'pie',
    },
    labels: ['進捗', 'ブロック', '未実行'],
    colors: ['#2f7ed8', '#f45b5b', '#90ed7d'],
    legend: {
      position: 'right',
    },
  };

  const pieSeries = [100, 0, 0];

  return (
    <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px' }}>
      <Card sx={{ p: 2 }}>
        <Typography variant="h5" textAlign={'center'}>
          テスト進行状況
        </Typography>
        <Chart options={pieOptions} series={pieSeries} type="pie" height={400} />
      </Card>
      <Card sx={{ p: 2 }}>
        <Typography variant="h5" textAlign={'center'}>
          テスト実行状況
        </Typography>
        <Chart options={barOptions} series={barSeries} type="bar" height={400} />
      </Card>
    </div>
  );
};

export default TestCharts;
