import type { NavItemConfig } from '@/types/nav';
import { paths } from '@/paths';

export const navItems = [
  { key: 'overview', title: '概要', href: paths.dashboard.overview, icon: 'chart-pie' },
  { key: 'PRs', title: 'プルリクエスト', href: paths.dashboard.PRs, icon: 'PRs' },
  { key: 'testResult', title: 'Test Result', href: paths.dashboard.testResult, icon: 'testResult' },
  { key: 'issueStatus', title: 'Issue Status', href: paths.dashboard.issueStatus, icon: 'issueStatus' },
] satisfies NavItemConfig[];
