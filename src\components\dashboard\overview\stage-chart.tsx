'use client';

import * as React from 'react';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import CardHeader from '@mui/material/CardHeader';
import Skeleton from '@mui/material/Skeleton';
import type { SxProps } from '@mui/material/styles';
import { ApexOptions } from 'apexcharts';

import { Chart } from '@/components/core/chart';

export function StageChart({
  sx,
  view,
  categories,
  data,
  loading,
}: {
  sx?: SxProps;
  view: 'month' | 'week';
  categories: string[];
  data: number[];
  loading?: boolean;
}): React.JSX.Element {

  const chartSeries = [
    {
      name: '件数',
      data: data,
    },
  ];

  const getRandomColor = () =>
    `#${Math.floor(Math.random() * 16777215)
      .toString(16)
      .padStart(6, '0')}`;
  const barColors = categories.map(() => getRandomColor());

  const chartOptions: ApexOptions = {
    chart: {
      type: 'bar',
      background: 'transparent',
      stacked: false,
      toolbar: { show: false },
    },
    plotOptions: {
      bar: {
        horizontal: false,
        columnWidth: '55%',
        borderRadius: 6,
        distributed: true,
        dataLabels: {
          position: 'top',
        },
      },
    },
    colors: barColors,
    dataLabels: { enabled: false },
    stroke: { show: true, width: 2, colors: ['transparent'] },
    xaxis: {
      categories,
      labels: {
        rotate: -30,
        style: { fontSize: '15px', fontWeight: 500 },
      },
    },
    yaxis: {
      min: 0,
      max: Math.max(...data, 60),
      tickAmount: 6,
      labels: {
        style: { fontSize: '15px', fontWeight: 500 },
        formatter: (value) => Math.round(value).toString(),
      },
    },
    fill: { opacity: 0.8 },
    legend: { show: false },
    tooltip: { shared: true, intersect: false },
    grid: {
      borderColor: '#eee',
      strokeDashArray: 2,
      xaxis: { lines: { show: false } },
      yaxis: { lines: { show: true } },
    },
  };

  return (
    <Card sx={sx}>
      <CardHeader
        title={
          <span style={{ fontSize: 28, fontWeight: 600, textAlign: 'center', width: '100%', display: 'block' }}>
            工程別バグ分布
          </span>
        }
        sx={{ textAlign: 'center', pb: 0 }}
      />
      <CardContent>
        {loading ? (
          <Skeleton variant="rectangular" height={300} sx={{ borderRadius: 3 }} />
        ) : (
          <Chart
            height={300}
            options={{
              ...chartOptions,
              chart: { ...chartOptions.chart, type: 'bar' },
              plotOptions: {
                bar: {
                  horizontal: false,
                  columnWidth: '55%',
                  borderRadius: 6,
                  distributed: true, // Make each bar a different color
                  dataLabels: {
                    position: 'top',
                  },
                },
              },
              colors: barColors,
              xaxis: {
                ...(chartOptions.xaxis || {}),
                labels: {
                  ...((chartOptions.xaxis && (chartOptions.xaxis as any).labels) || {}),
                  rotate: -30,
                  style: { fontSize: '15px', fontWeight: 500 },
                },
              },
              yaxis:
                Array.isArray(chartOptions.yaxis) &&
                chartOptions.yaxis.length > 0 &&
                typeof chartOptions.yaxis[0] === 'object'
                  ? (chartOptions.yaxis as Array<any>).map((y: any) => ({
                      ...y,
                      min: 0,
                      max: undefined,
                      tickAmount: 6,
                      labels: {
                        ...(y.labels || {}),
                        style: { fontSize: '15px', fontWeight: 500 },
                      },
                    }))
                  : {
                      ...(chartOptions.yaxis || {}),
                      min: 0,
                      max: undefined,
                      tickAmount: 6,
                      labels: {
                        ...((chartOptions.yaxis && (chartOptions.yaxis as any).labels) || {}),
                        style: { fontSize: '15px', fontWeight: 500 },
                      },
                    },
              grid: {
                ...chartOptions.grid,
                yaxis: { lines: { show: true } },
                xaxis: { lines: { show: false } },
              },
            }}
            series={chartSeries}
            type="bar"
            width="100%"
          />
        )}
      </CardContent>
    </Card>
  );
}
