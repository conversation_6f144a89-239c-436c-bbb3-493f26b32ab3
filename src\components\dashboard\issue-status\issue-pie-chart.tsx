import React from 'react';
import { Card } from '@mui/material';
import ReactApex<PERSON>hart from 'react-apexcharts';

export default function IssuePieChart() {
  const series = [5, 10, 20]; // mockup data

  const options: ApexCharts.ApexOptions = {
    chart: {
      type: 'pie',
    },
    labels: ['クリティカル', '主要', 'マイナー'],
    colors: ['#d32f2f', '#81c784', '#ffeb3b'], // red, green, yellow
    legend: {
      position: 'right',
    },
    title: {
      text: '未解決の欠陥',
      align: 'center',
    },
  };

  return (
    <Card sx={{ p: 2 }}>
      <ReactApexChart options={options} series={series} type="pie" height={400} />;
    </Card>
  );
}
