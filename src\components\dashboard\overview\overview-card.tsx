import * as React from 'react';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import ArrowDropUpIcon from '@mui/icons-material/ArrowDropUp';
import Avatar from '@mui/material/Avatar';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import Stack from '@mui/material/Stack';
import type { SxProps } from '@mui/material/styles';
import Typography from '@mui/material/Typography';

export interface OverviewCardProps {
  sx?: SxProps;
  value: string;
  label: string;
  icon: React.ReactNode;
  avatarColor: string;
  diff?: number;
  trend?: 'up' | 'down';
  view?: 'month' | 'week';
}

function TrendPill({ diff, trend }: { diff: number; trend: 'up' | 'down' }) {
  const isUp = trend === 'up';
  return (
    <Stack
      direction="row"
      spacing={0.5}
      sx={{
        alignItems: 'center',
        backgroundColor: isUp ? 'success.main' : 'error.main',
        color: '#fff',
        borderRadius: '999px',
        padding: '0 8px 0 4px',
        minWidth: 48,
        height: 22,
        fontWeight: 700,
        fontSize: '1rem',
        justifyContent: 'center',
        marginBottom: 0.75,
      }}
    >
      {isUp ? (
        <ArrowDropUpIcon style={{ color: '#fff', fontSize: 24, marginRight: -6 }} />
      ) : (
        <ArrowDropDownIcon style={{ color: '#fff', fontSize: 24, marginRight: -6 }} />
      )}
      <span>{isUp ? `+${diff}` : diff}</span>
    </Stack>
  );
}

function NeutralPill() {
  return (
    <Stack
      direction="row"
      spacing={0.5}
      sx={{
        alignItems: 'center',
        backgroundColor: '#5c6674',
        color: '#fff',
        borderRadius: '999px',
        minWidth: 48,
        height: 24,
        fontWeight: 700,
        fontSize: '1rem',
        justifyContent: 'center',
        marginBottom: 0.75,
      }}
    >
      <span style={{ fontWeight: 600, fontSize: '1.1em' }}>±0</span>
    </Stack>
  );
}

export function OverviewCard({
  sx,
  value,
  label,
  icon,
  avatarColor,
  diff,
  trend,
  view = 'month',
}: OverviewCardProps): React.JSX.Element {
  return (
    <Card sx={sx}>
      <CardContent>
        <Stack spacing={2}>
          <Stack direction="row" sx={{ alignItems: 'flex-start', justifyContent: 'space-between' }} spacing={3}>
            <Stack spacing={1}>
              <Typography sx={{ fontWeight: '700' }} color="text.secondary" variant="overline">
                {label}
              </Typography>
              <Stack sx={{ alignItems: 'flex-end' }} direction="row" spacing={1}>
                <Typography sx={{ fontWeight: '700' }} variant="h4">
                  {value}
                </Typography>
                {diff !== undefined && trend && diff !== 0 ? <TrendPill diff={diff} trend={trend} /> : null}
                {diff === 0 ? <NeutralPill /> : null}
              </Stack>
            </Stack>
            <Avatar sx={{ backgroundColor: avatarColor, height: '56px', width: '56px' }}>{icon}</Avatar>
          </Stack>
        </Stack>
      </CardContent>
    </Card>
  );
}
