'use client';

import * as React from 'react';
import Link from 'next/link';
import {
  Box,
  Card,
  Chip,
  Divider,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TablePagination,
  TableRow,
  TableSortLabel,
} from '@mui/material';
import Skeleton from '@mui/material/Skeleton';
import Tooltip from '@mui/material/Tooltip';
import dayjs from 'dayjs';

import { PRsFilters } from './prs-filters';

export interface AuthorUser {
  id: number;
  githubId: string;
  login: string;
  avatarUrl: string | null;
  htmlUrl: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface PullRequest {
  githubId: string;
  title: string;
  body: string | null;
  state: string;
  createdAt: string;
  mergedAt: string | null;
  analyzed: boolean;
  analyzedAt: string;
  prNumber: number;
  entityCreatedAt: string;
  entityUpdatedAt: string;
  authorUserId: number;
  authorUser: AuthorUser;
  amountComments: number;
  score: number;
}

interface PRsTableProps {
  prs: PullRequest[];
  count?: number;
  page?: number;
  rowsPerPage?: number;
  search?: string;
  onSearchChange?: (value: string) => void;
  statusFilter?: string;
  onStatusFilterChange?: (value: string) => void;
  onPageChange?: (page: number) => void;
  onRowsPerPageChange?: (rows: number) => void;
  isLoading?: boolean;
}

export function PRsTable({
  prs = [],
  count = 0,
  page = 1,
  rowsPerPage = 10,
  search = '',
  onSearchChange,
  statusFilter = '',
  onStatusFilterChange,
  onPageChange,
  onRowsPerPageChange,
  isLoading = false,
}: PRsTableProps): React.JSX.Element {
  const [orderBy, setOrderBy] = React.useState<'title' | 'state' | 'author' | 'createdAt'>('createdAt');
  const [order, setOrder] = React.useState<'asc' | 'desc'>('desc');
  const [authorFilter, setAuthorFilter] = React.useState('');

  return (
    <>
      <PRsFilters
        search={search}
        onSearchChange={onSearchChange ?? (() => {})}
        statusFilter={statusFilter}
        onStatusFilterChange={onStatusFilterChange ?? (() => {})}
        authorFilter={authorFilter}
        onAuthorFilterChange={setAuthorFilter}
      />
      <Card>
        <Table sx={{ minWidth: '800px' }} stickyHeader>
          <TableHead>
            <TableRow>
              <TableCell>#PR</TableCell>
              <TableCell
                sx={{
                  maxWidth: 200,
                  minWidth: 120,
                  width: 200,
                  whiteSpace: 'normal',
                  wordBreak: 'break-word',
                }}
              >
                タイトル
              </TableCell>
              <TableCell
                sx={{
                  maxWidth: 320,
                  minWidth: 200,
                  width: 320,
                  whiteSpace: 'normal',
                  wordBreak: 'break-word',
                }}
              >
                説明
              </TableCell>
              <TableCell>開発者</TableCell>
              <TableCell>品質スコア</TableCell>
              <TableCell>コメント数</TableCell>
              <TableCell>ステータス</TableCell>
              <TableCell>作成日</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {isLoading ? (
              [...Array(rowsPerPage)].map((_, i) => (
                <TableRow key={i}>
                  <TableCell>
                    <Skeleton width={40} />
                  </TableCell>
                  <TableCell>
                    <Skeleton width={220} />
                  </TableCell>
                  <TableCell>
                    <Skeleton width={100} />
                  </TableCell>
                  <TableCell>
                    <Skeleton width={60} />
                  </TableCell>
                  <TableCell>
                    <Skeleton width={60} />
                  </TableCell>
                  <TableCell>
                    <Skeleton width={80} />
                  </TableCell>
                  <TableCell>
                    <Skeleton width={100} />
                  </TableCell>
                </TableRow>
              ))
            ) : prs.length === 0 ? (
              <TableRow>
                <TableCell colSpan={8} align="center">
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      minHeight: 300,
                      fontWeight: 700,
                      fontSize: 24,
                    }}
                  >
                    データなし
                  </Box>
                </TableCell>
              </TableRow>
            ) : (
              prs.map((pr) => (
                <TableRow key={pr.githubId} hover>
                  <TableCell>
                    <Link
                      href={`https://github.com/sotatek-dev/TDC-code-review-analytics-mockdata/pull/${pr.prNumber}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      style={{ textDecoration: 'underline', fontWeight: 600 }}
                    >
                      #{pr.prNumber ?? pr.githubId}
                    </Link>
                  </TableCell>
                  <TableCell
                    sx={{
                      maxWidth: 200,
                      minWidth: 120,
                      width: 200,
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap',
                    }}
                  >
                    <Tooltip title={pr.title} placement="top" arrow>
                      <span
                        style={{
                          fontWeight: 600,
                          display: 'block',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap',
                        }}
                      >
                        {pr.title}
                      </span>
                    </Tooltip>
                  </TableCell>
                  <TableCell
                    sx={{
                      maxWidth: 320,
                      minWidth: 200,
                      width: 320,
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap',
                    }}
                  >
                    <Tooltip
                      title={pr.body ? <span style={{ whiteSpace: 'pre-line' }}>{pr.body}</span> : ''}
                      placement="top"
                      arrow
                    >
                      <span
                        style={{
                          color: '#888',
                          fontSize: 13,
                          display: 'block',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap',
                        }}
                      >
                        {pr.body}
                      </span>
                    </Tooltip>
                  </TableCell>
                  <TableCell sx={{ maxWidth: 120, overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
                    <Tooltip title={pr.authorUser.login} placement="top" arrow>
                      <span
                        style={{
                          display: 'block',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap',
                        }}
                      >
                        {pr.authorUser.login}
                      </span>
                    </Tooltip>
                  </TableCell>
                  <TableCell>
                    <Tooltip title={typeof pr.score === 'number' ? pr.score.toString() : ''} placement="top" arrow>
                      <span>{typeof pr.score === 'number' ? pr.score : '-'}</span>
                    </Tooltip>
                  </TableCell>
                  <TableCell>
                    <span>{pr.amountComments}</span>
                  </TableCell>
                  <TableCell>
                    <span>
                      <Chip
                        sx={{ fontWeight: 600 }}
                        label={pr.state.charAt(0).toUpperCase() + pr.state.slice(1)}
                        size="small"
                        color={
                          pr.state === 'open'
                            ? 'success'
                            : pr.state === 'closed'
                              ? 'error'
                              : pr.state === 'merged'
                                ? 'primary'
                                : 'default'
                        }
                      />
                    </span>
                  </TableCell>
                  <TableCell>
                    <span>{dayjs(pr.createdAt).format('YYYY年MM月DD日')}</span>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
        <Divider />
        <TablePagination
          component="div"
          count={count}
          page={page - 1}
          onPageChange={(_, newPage) => onPageChange?.(newPage + 1)}
          rowsPerPage={rowsPerPage}
          onRowsPerPageChange={(e) => onRowsPerPageChange?.(parseInt(e.target.value, 10))}
          rowsPerPageOptions={[10, 30, 50]}
          labelRowsPerPage="1ページあたりの行数："
          labelDisplayedRows={({ from, to }) => `${from}〜${to}件 / 全${count !== -1 ? count : `more than ${to}件`}`}
        />
      </Card>
    </>
  );
}
