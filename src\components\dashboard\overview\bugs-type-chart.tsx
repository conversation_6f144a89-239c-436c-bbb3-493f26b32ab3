import * as React from 'react';
import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import CardHeader from '@mui/material/CardHeader';
import Divider from '@mui/material/Divider';
import type { SxProps } from '@mui/material/styles';

import { Chart } from '@/components/core/chart';

export interface BugTypeChartProps {
  sx?: SxProps;
  view?: 'month' | 'week';
  data?: { commentCategory: string; count: number }[];
}

const bugTypeLabels = [
  'CODE_QUALITY',
  'BUSINESS_LOGIC',
  'PERFORMANCE',
  'SECURITY',
  'TESTING',
  'ARCHITECTURE',
  'DOCUMENTATION',
  'PRAISE',
  'OTHER',
];
const bugTypeColors = [
  '#E57373', // CODE_QUALITY (Red)
  '#FBC02D', // BUSINESS_LOGIC (Amber)
  '#64B5F6', // PERFORMANCE (Blue)
  '#BA68C8', // SECURITY (Purple)
  '#4DB6AC', // TESTING (Teal)
  '#FF8A65', // ARCHITECTURE (Orange)
  '#FFD54F', // DOCUMENTATION (Yellow)
  '#81C784', // PRAISE (Green)
  '#A1887F', // OTHER (Brown)
];

export function BugTypeChart({ sx, view = 'month', data }: BugTypeChartProps): React.JSX.Element {
  let bugTypeData: number[] = new Array(bugTypeLabels.length).fill(0);
  if (data) {
    // Map data to the correct order of bugTypeLabels
    const dataMap = Object.fromEntries(data.map((d) => [d.commentCategory, d.count]));
    bugTypeData = bugTypeLabels.map((label) => dataMap[label] ?? 0);
  }

  // Compose labels with counts for each category
  const total = bugTypeData?.reduce((sum, v) => sum + v, 0) ?? 0;

  const chartOptions = {
    chart: {
      type: 'donut' as const,
      background: 'transparent',
    },
    labels: bugTypeLabels,
    colors: bugTypeColors,
    legend: {
      position: 'bottom' as const,
    } as const,
    dataLabels: {
      enabled: true,
      formatter: function (val: number, opts: any) {
        return bugTypeData[opts.seriesIndex];
      },
      style: {
        fontSize: '14px',
        fontWeight: 700,
        colors: ['#fff'],
      },
      dropShadow: {
        enabled: false,
      },
    },
    plotOptions: {
      pie: {
        donut: {
          size: '70%',
          labels: {
            show: false,
          },
        },
      },
    },
    stroke: { width: 0 },
    tooltip: { enabled: true },
  };

  return (
    <Card sx={sx}>
      <CardHeader
        title={
          <span style={{ fontSize: 26, fontWeight: 600, textAlign: 'center', width: '100%', display: 'block' }}>
            バグ種別の内訳
          </span>
        }
        sx={{ textAlign: 'center', pb: 0 }}
      />
      <Box sx={{ p: 2, pb: 0, position: 'relative' }}>
        <Chart height={380} options={chartOptions} series={bugTypeData} type="donut" width="100%" />
        <Box
          sx={{
            position: 'absolute',
            top: '45%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            pointerEvents: 'none',
            textAlign: 'center',
          }}
        >
          <div style={{ fontSize: 18, color: '#333', fontWeight: 600 }}>合計</div>
          <div style={{ fontSize: 32, color: '#333', fontWeight: 900 }}>{total}</div>
        </Box>
      </Box>
    </Card>
  );
}
