'use client';

import Image from 'next/image';
import { Box, Stack } from '@mui/material';

import TestCharts from '@/components/dashboard/test-result/test-charts';
import TestRateChart from '@/components/dashboard/test-result/test-rate-chart';

// import TestResultTable from '@/components/dashboard/test-result/test-result-table';

const TestResultClientPage = () => {
  return (
    <Stack spacing={10} direction="column">
      <Box justifyContent={'center'}>
        <Image
          src={'/assets/test-table.png'}
          alt="test-table"
          width={1000}
          height={500}
          style={{ width: '100%', height: 'auto' }}
        />
      </Box>
      {/* <TestResultTable /> */}
      <TestRateChart />
      <TestCharts />
    </Stack>
  );
};

export default TestResultClientPage;
