import React from 'react';
import { Box, Card, Grid, Stack } from '@mui/material';
import { ApexOptions } from 'apexcharts';
import Chart from 'react-apexcharts';

export default function JiraCharts() {
  const cumulativeOptions: ApexOptions = {
    chart: {
      type: 'bar', // ✅ phải là literal "bar"
      stacked: false,
      toolbar: { show: false },
    },
    plotOptions: {
      bar: {
        columnWidth: '40%',
      },
    },
    xaxis: {
      categories: ['11-03', '11-10', '11-17', '11-24', '12-01', '12-08', '12-15', '12-22', '12-29', '01-05'],
    },
    title: {
      text: 'JIRA課題累積状況',
      align: 'center',
      style: { fontSize: '20px', fontWeight: 'bold' },
    },
    legend: {
      position: 'right',
    },
  };

  const cumulativeSeries = [
    {
      name: '累積未解決',
      data: [200, 150, 400, 0, 0, 0, 300, 240, 0, 0],
      color: '#d62728',
    },
    {
      name: '累積解決',
      data: [0, 100, 250, 0, 0, 0, 400, 290, 0, 0],
      color: '#1f77b4',
    },
  ];

  const dailyOptions: ApexOptions = {
    chart: {
      type: 'bar', // ✅ literal type
      stacked: false,
      toolbar: { show: false },
    },
    plotOptions: {
      bar: {
        columnWidth: '40%',
      },
    },
    xaxis: {
      categories: [
        '11-03',
        '11-05',
        '11-07',
        '11-09',
        '11-11',
        '11-13',
        '11-15',
        '11-17',
        '11-19',
        '11-21',
        '11-23',
        '11-25',
      ],
    },
    title: {
      text: 'JIRA課題処理状況',
      align: 'center',
      style: { fontSize: '20px', fontWeight: 'bold' },
    },
    legend: {
      position: 'right',
    },
  };

  const dailySeries = [
    {
      name: '解決',
      data: [0, 3, 0, 3, 0, 0, 0, 6, 7, 4, 0, 0],
      color: '#d62728',
    },
    {
      name: '未解決',
      data: [0, 1, 3, 1, 1, 1, 1, 2, 3, 0, 0, 1],
      color: '#1f77b4',
    },
  ];

  return (
    <Box sx={{ flexGrow: 1, p: 2 }}>
      <Grid container spacing={4}>
        <Grid item xs={12} md={6}>
          <Card sx={{ minWidth: 300, p: 2 }}>
            <Chart options={cumulativeOptions} series={cumulativeSeries} type="bar" height={400} />
          </Card>
        </Grid>
        <Grid item xs={12} md={6}>
          <Card sx={{ minWidth: 400, p: 2 }}>
            <Chart options={dailyOptions} series={dailySeries} type="bar" height={400} />
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
}
