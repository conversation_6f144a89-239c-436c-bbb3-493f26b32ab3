'use client';

import * as React from 'react';
import Image from 'next/image';
import { Box, Container, Grid } from '@mui/material';

import JiraStatusChart from '@/components/dashboard/issue-status/issue-bar-chart';
import Issue<PERSON>ieChart from '@/components/dashboard/issue-status/issue-pie-chart';
import JiraCharts from '@/components/dashboard/issue-status/Jira-charts';

const IssueStatusClientPage = () => {
  return (
    <Container maxWidth="xl">
      <Box justifyContent={'center'}>
        <Image
          src={'/assets/table-issue.png'}
          alt="issue-status"
          width={1000}
          height={500}
          style={{ width: '100%', height: 'auto' }}
        />
      </Box>
      <Box sx={{ flexGrow: 1, p: 2 }}>
        <Grid container spacing={4}>
          <Grid item xs={12} md={6}>
            <Box sx={{ minWidth: 300 }}>
              <IssuePieChart />
            </Box>
          </Grid>
          <Grid item xs={12} md={6}>
            <Box sx={{ minWidth: 400 }}>
              <JiraStatusChart />
            </Box>
          </Grid>
        </Grid>
      </Box>
      <JiraCharts />
    </Container>
  );
};

export default IssueStatusClientPage;
