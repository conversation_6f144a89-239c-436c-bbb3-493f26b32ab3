'use client';

import * as React from 'react';
import type { Metadata } from 'next';
import Stack from '@mui/material/Stack';

import { config } from '@/config';
import { useBaseQuery } from '@/hooks/use-base-query';
import { PRsTable, PullRequest } from '@/components/dashboard/pull-requests/prs-table';

export const metadata = { title: `Customers | Dashboard | ${config.site.name}` } satisfies Metadata;

export default function PullRequestsClientPage(): React.JSX.Element {
  const [page, setPage] = React.useState(1);
  const [rowsPerPage, setRowsPerPage] = React.useState(10);
  const [search, setSearch] = React.useState('');
  const [status, setStatus] = React.useState('');

  const { data: prsData, isLoading } = useBaseQuery<{
    message: string;
    data: { pullRequests: PullRequest[]; total: number; page: number; limit: number };
  }>(
    ['pull-requests', page, rowsPerPage, search, status],
    `/api/proxy/prs?page=${page}&limit=${rowsPerPage}` +
      (search ? `&search=${encodeURIComponent(search)}` : '') +
      (status ? `&status=${status}` : '')
  );
  
  const prs: PullRequest[] = prsData?.data.pullRequests ?? [];
  const count = prsData?.data.total ?? 0;

  const handleStatusChange = (value: string) => {
    setStatus(value.toLowerCase());
    setPage(1);
  };

  return (
    <Stack spacing={3}>
      <PRsTable
        prs={prs}
        count={count}
        page={page}
        rowsPerPage={rowsPerPage}
        search={search}
        onSearchChange={setSearch}
        statusFilter={status}
        onStatusFilterChange={handleStatusChange}
        onPageChange={setPage}
        onRowsPerPageChange={(newRows) => {
          setRowsPerPage(newRows);
          setPage(1);
        }}
        isLoading={isLoading}
      />
    </Stack>
  );
}
