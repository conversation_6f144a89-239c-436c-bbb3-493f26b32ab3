export const codeQualityTrend = {
  message: 'Fake code quality trend data fetched successfully',
  data: [
    { day: '06-11', qualityScore: 7.2, commentCount: 12 },
    { day: '06-12', qualityScore: 7.8, commentCount: 8 },
    { day: '06-13', qualityScore: 6.9, commentCount: 15 },
    { day: '06-14', qualityScore: 7.9, commentCount: 6 },
    { day: '06-15', qualityScore: 7.5, commentCount: 9 },
    { day: '06-16', qualityScore: 8, commentCount: 4 },
    { day: '06-17', qualityScore: 7.8, commentCount: 7 },
  ],
};

export const bugsData = [
  {
    id: '1',
    title: 'Login page crashes on submit',
    status: 'open',
    severity: 'MAJOR',
    createdAt: '2025-01-15',
    resolvedAt: undefined,
  },
  {
    id: '2',
    title: 'Dashboard not loading for some users',
    status: 'closed',
    severity: 'CRITICAL',
    createdAt: '2025-02-10',
    resolvedAt: '2025-02-15',
  },
  {
    id: '3',
    title: 'Typo in settings page',
    status: 'closed',
    severity: 'NITPICK',
    createdAt: '2025-03-05',
    resolvedAt: '2025-03-07',
  },
  {
    id: '4',
    title: 'API returns 500 error on save',
    status: 'open',
    severity: 'MAJOR',
    createdAt: '2025-04-12',
    resolvedAt: undefined,
  },
  {
    id: '5',
    title: 'Profile image upload fails',
    status: 'closed',
    severity: 'MINOR',
    createdAt: '2025-05-20',
    resolvedAt: '2025-05-25',
  },
  {
    id: '6',
    title: 'Notification badge not updating',
    status: 'open',
    severity: 'NITPICK',
    createdAt: '2025-06-01',
    resolvedAt: undefined,
  },
  {
    id: '7',
    title: 'Export to CSV not working',
    status: 'closed',
    severity: 'MAJOR',
    createdAt: '2025-07-10',
    resolvedAt: '2025-07-15',
  },
  {
    id: '8',
    title: 'Sidebar menu overlaps content',
    status: 'open',
    severity: 'MAJOR',
    createdAt: '2025-08-18',
    resolvedAt: undefined,
  },
  {
    id: '9',
    title: 'Search returns incomplete results',
    status: 'closed',
    severity: 'CRITICAL',
    createdAt: '2025-09-05',
    resolvedAt: '2025-09-10',
  },
  {
    id: '10',
    title: 'Settings not saving',
    status: 'open',
    severity: 'CRITICAL',
    createdAt: '2025-10-22',
    resolvedAt: undefined,
  },
  {
    id: '11',
    title: 'Dark mode not applied on reload',
    status: 'closed',
    severity: 'MAJOR',
    createdAt: '2025-01-25',
    resolvedAt: '2025-02-01',
  },
  {
    id: '12',
    title: 'User cannot reset password',
    status: 'open',
    severity: 'MAJOR',
    createdAt: '2025-03-15',
    resolvedAt: undefined,
  },
  {
    id: '13',
    title: 'Mobile layout broken on iOS',
    status: 'closed',
    severity: 'CRITICAL',
    createdAt: '2025-04-02',
    resolvedAt: '2025-04-10',
  },
  {
    id: '14',
    title: 'Email notifications sent twice',
    status: 'open',
    severity: 'NITPICK',
    createdAt: '2025-05-12',
    resolvedAt: undefined,
  },
  {
    id: '15',
    title: 'File upload progress stuck at 99%',
    status: 'closed',
    severity: 'MAJOR',
    createdAt: '2025-06-18',
    resolvedAt: '2025-06-20',
  },
  {
    id: '16',
    title: 'Comment box not visible for guests',
    status: 'open',
    severity: 'MAJOR',
    createdAt: '2025-07-22',
    resolvedAt: undefined,
  },
  {
    id: '17',
    title: 'App freezes after logout',
    status: 'closed',
    severity: 'CRITICAL',
    createdAt: '2025-08-03',
    resolvedAt: '2025-08-05',
  },
  {
    id: '18',
    title: 'Settings page loads slowly',
    status: 'open',
    severity: 'NITPICK',
    createdAt: '2025-09-14',
    resolvedAt: undefined,
  },
  {
    id: '19',
    title: 'User avatar not updating',
    status: 'closed',
    severity: 'MAJOR',
    createdAt: '2025-10-01',
    resolvedAt: '2025-10-03',
  },
  {
    id: '20',
    title: 'Search bar overlaps logo',
    status: 'open',
    severity: 'NITPICK',
    createdAt: '2025-11-11',
    resolvedAt: undefined,
  },
  {
    id: '21',
    title: 'Button click event not firing',
    status: 'open',
    severity: 'MAJOR',
    createdAt: '2025-01-05',
    resolvedAt: undefined,
  },
  {
    id: '22',
    title: 'Dropdown menu not closing on outside click',
    status: 'closed',
    severity: 'NITPICK',
    createdAt: '2025-02-18',
    resolvedAt: '2025-02-20',
  },
  {
    id: '23',
    title: 'Footer links broken on mobile',
    status: 'open',
    severity: 'CRITICAL',
    createdAt: '2025-03-22',
    resolvedAt: undefined,
  },
  {
    id: '24',
    title: 'User session expires too early',
    status: 'closed',
    severity: 'CRITICAL',
    createdAt: '2025-04-15',
    resolvedAt: '2025-04-18',
  },
  {
    id: '25',
    title: 'Sidebar not scrollable',
    status: 'open',
    severity: 'MAJOR',
    createdAt: '2025-05-09',
    resolvedAt: undefined,
  },
  {
    id: '26',
    title: 'Tooltip text overlaps icon',
    status: 'closed',
    severity: 'NITPICK',
    createdAt: '2025-06-13',
    resolvedAt: '2025-06-14',
  },
  {
    id: '27',
    title: 'API rate limit error on dashboard',
    status: 'open',
    severity: 'CRITICAL',
    createdAt: '2025-07-01',
    resolvedAt: undefined,
  },
  {
    id: '28',
    title: 'Search filter not resetting',
    status: 'closed',
    severity: 'MAJOR',
    createdAt: '2025-08-21',
    resolvedAt: '2025-08-23',
  },
  {
    id: '29',
    title: 'Table header misaligned',
    status: 'open',
    severity: 'NITPICK',
    createdAt: '2025-09-30',
    resolvedAt: undefined,
  },
  {
    id: '30',
    title: 'File download fails on Safari',
    status: 'closed',
    severity: 'MAJOR',
    createdAt: '2025-10-10',
    resolvedAt: '2025-10-12',
  },
  {
    id: '31',
    title: 'Modal dialog not centered',
    status: 'open',
    severity: 'MAJOR',
    createdAt: '2025-11-03',
    resolvedAt: undefined,
  },
  {
    id: '32',
    title: 'Date picker not showing weekends',
    status: 'closed',
    severity: 'NITPICK',
    createdAt: '2025-12-01',
    resolvedAt: '2025-12-03',
  },
  {
    id: '33',
    title: 'Chart axis labels overlap',
    status: 'open',
    severity: 'MAJOR',
    createdAt: '2025-12-10',
    resolvedAt: undefined,
  },
  {
    id: '34',
    title: 'Push notifications not received',
    status: 'closed',
    severity: 'CRITICAL',
    createdAt: '2025-11-20',
    resolvedAt: '2025-11-22',
  },
  {
    id: '35',
    title: 'User cannot change email address',
    status: 'open',
    severity: 'MAJOR',
    createdAt: '2025-10-28',
    resolvedAt: undefined,
  },
  {
    id: '36',
    title: 'Loading spinner never disappears',
    status: 'closed',
    severity: 'MAJOR',
    createdAt: '2025-09-17',
    resolvedAt: '2025-09-18',
  },
  {
    id: '37',
    title: 'Form validation error not shown',
    status: 'open',
    severity: 'NITPICK',
    createdAt: '2025-08-29',
    resolvedAt: undefined,
  },
  {
    id: '38',
    title: 'App slow after update',
    status: 'closed',
    severity: 'MAJOR',
    createdAt: '2025-07-19',
    resolvedAt: '2025-07-21',
  },
  {
    id: '39',
    title: 'Sidebar icons missing',
    status: 'open',
    severity: 'MAJOR',
    createdAt: '2025-06-25',
    resolvedAt: undefined,
  },
  {
    id: '40',
    title: 'User gets logged out randomly',
    status: 'closed',
    severity: 'CRITICAL',
    createdAt: '2025-05-30',
    resolvedAt: '2025-06-02',
  },
];

export const testResultData = [
  { category: '全体統計', total: 3681, pass: 3499, fail: 0, na: 182, block: 0, noRun: 0 },
  { category: 'メイン', total: 400, pass: 400, fail: 0, na: 0, block: 0, noRun: 0 },
  { category: '検索', total: 87, pass: 87, fail: 0, na: 0, block: 0, noRun: 0 },
  { category: 'コンテンツ', total: 361, pass: 328, fail: 0, na: 33, block: 0, noRun: 0 },
  { category: 'ライブラリ', total: 114, pass: 109, fail: 0, na: 5, block: 0, noRun: 0 },
  { category: 'マイページ', total: 1328, pass: 1235, fail: 0, na: 93, block: 0, noRun: 0 },
  { category: 'クラス', total: 1391, pass: 1340, fail: 0, na: 51, block: 0, noRun: 0 },
];
