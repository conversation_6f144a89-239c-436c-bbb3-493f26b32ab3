'use client';

import * as React from 'react';
import { getLastNDates } from '@/utils/date';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import CardHeader from '@mui/material/CardHeader';
import Divider from '@mui/material/Divider';
import type { SxProps } from '@mui/material/styles';
import { useTheme } from '@mui/material/styles';
import type { ApexOptions } from 'apexcharts';
import dayjs from 'dayjs';

import { Chart } from '@/components/core/chart';

export interface Bug {
  id: string;
  title: string;
  status: string;
  severity: string;
  createdAt: string;
  resolvedAt?: string;
}

export interface CodeQualityChartsProps {
  data: { day: string; qualityScore: number; commentCount: number }[];
  sx?: SxProps;
  view: 'month' | 'week';
}

export function CodeQualityCharts({ data, sx, view }: CodeQualityChartsProps): React.JSX.Element {
  const categories = data.map((d) => dayjs(d.day, 'MM-DD').format('MM月DD日'));
  const qualityScore = data.map((d) => d.qualityScore);
  const commentCount = data.map((d) => d.commentCount);

  const chartSeries = [
    {
      name: '品質スコア',
      type: 'area',
      data: qualityScore,
    },
    {
      name: 'コメント数',
      type: 'area',
      data: commentCount,
    },
  ];
  const chartOptions = useCodeQualityChartsOptions(categories);

  return (
    <Card sx={sx}>
      <CardHeader
        title={
          <span style={{ fontSize: 28, fontWeight: 600, textAlign: 'center', width: '100%', display: 'block' }}>
            コード品質の推移
          </span>
        }
        sx={{ textAlign: 'center', pb: 0 }}
      />
      <CardContent>
        {/* Custom Legend */}
        <div style={{ display: 'flex', justifyContent: 'center', gap: 24, marginBottom: 8 }}>
          <span style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
            <span
              style={{
                width: 24,
                height: 6,
                background: '#4F8CFF',
                border: '2px solid #4F8CFF',
                borderRadius: 2,
                marginRight: 4,
              }}
            />
            <span style={{ color: '#4F8CFF', fontWeight: 500 }}>品質スコア</span>
          </span>
          <span style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
            <span
              style={{
                width: 24,
                height: 6,
                background: '#FF5C5C',
                border: '2px solid #FF5C5C',
                borderRadius: 2,
                marginRight: 4,
              }}
            />
            <span style={{ color: '#FF5C5C', fontWeight: 500 }}>コメント数</span>
          </span>
        </div>
        <Chart height={350} options={chartOptions} series={chartSeries} type="line" width="100%" />
      </CardContent>
      <Divider />
    </Card>
  );
}

function useCodeQualityChartsOptions(days: string[]): ApexOptions {
  const theme = useTheme();
  return {
    chart: { background: 'transparent', stacked: false, toolbar: { show: false } },
    colors: ['#4F8CFF', '#FF5C5C'],
    dataLabels: { enabled: false },
    fill: { type: ['solid', 'solid'], opacity: [0.2, 0.2] },
    grid: {
      borderColor: theme.palette.divider,
      strokeDashArray: 2,
      xaxis: { lines: { show: false } },
      yaxis: { lines: { show: true } },
    },
    legend: { show: false },
    stroke: { curve: 'smooth', width: [3, 3] },
    theme: { mode: theme.palette.mode },
    xaxis: {
      categories: days,
      axisBorder: { color: theme.palette.divider, show: true },
      axisTicks: { color: theme.palette.divider, show: true },
      labels: { offsetY: 5, style: { colors: theme.palette.text.secondary, fontWeight: 500, fontSize: '16px' } },
    },
    yaxis: {
      min: 0,
      tickAmount: 8,
      labels: {
        formatter: (value) => `${value}`,
        offsetX: -10,
        style: { colors: theme.palette.text.secondary, fontWeight: 500, fontSize: '14px' },
      },
    },
    tooltip: {
      shared: true,
      intersect: false,
    },
  };
}
