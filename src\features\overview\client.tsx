'use client';

import * as React from 'react';
import MenuItem from '@mui/material/MenuItem';
import Select from '@mui/material/Select';
import Skeleton from '@mui/material/Skeleton';
import Stack from '@mui/material/Stack';
import Grid from '@mui/material/Unstable_Grid2';

import type { CodeQualityTrend } from '@/types/code-quality-trend';
import type { CommentCategoryCount } from '@/types/comment-category-count';
import type { CommentStageCount } from '@/types/comment-stage-count';
import type { DashboardSummary } from '@/types/dashboard-summary';
import type { TopDeveloper } from '@/types/top-developer';
import { useBaseQuery } from '@/hooks/use-base-query';
import { AvgQualityScore } from '@/components/dashboard/overview/avg-score';
import { BugTypeChart } from '@/components/dashboard/overview/bugs-type-chart';
import { CodeQualityCharts } from '@/components/dashboard/overview/code-quality-chart';
import { CriticalIssues } from '@/components/dashboard/overview/critical-issues';
import { StageChart } from '@/components/dashboard/overview/stage-chart';
import { TopDevelopers } from '@/components/dashboard/overview/top-developers';
import { TotalDevelopers } from '@/components/dashboard/overview/total-developers';
import { TotalPRs } from '@/components/dashboard/overview/total-prs';

export default function OverviewClientPage(): React.JSX.Element {
  const [view, setView] = React.useState<'month' | 'week'>('week');

  const { data: summary, isLoading: summaryLoading } = useBaseQuery<{ message: string; data: DashboardSummary }>(
    ['dashboard-summary', view],
    `/api/proxy/dashboard/summary?periodType=${view}`
  );
  const { data: codeQualityTrend, isLoading: trendLoading } = useBaseQuery<{
    message: string;
    data: CodeQualityTrend[];
  }>(['dashboard-code-quality-trend', view], `/api/proxy/dashboard/code-quality-trend?periodType=${view}`);

  const { data: topDevelopers, isLoading: topDevsLoading } = useBaseQuery<{ message: string; data: TopDeveloper[] }>(
    ['dashboard-top-developers', view],
    `/api/proxy/dashboard/top-developers?periodType=${view}`
  );
  const { data: commentCategoryCounts, isLoading: commentCatLoading } = useBaseQuery<{
    message: string;
    data: CommentCategoryCount[];
  }>(['dashboard-comment-category-counts', view], `/api/proxy/dashboard/comment-category-counts?periodType=${view}`);

  const { data: commentStageCounts, isLoading: commentStageLoading } = useBaseQuery<{
    message: string;
    data: CommentStageCount[];
  }>(['dashboard-comment-stage-counts', view], `/api/proxy/dashboard/comment-stage?periodType=${view}`);

  const overviewData = React.useMemo(() => {
    if (summary?.data) {
      const calculateTrend = (current: number | null, last: number | null) => {
        if (current === null || last === null) return undefined;
        const diff = current - last;
        return diff > 0 ? ('up' as const) : diff < 0 ? ('down' as const) : undefined;
      };

      const calculateDiff = (current: number | null, last: number | null) => {
        if (current === null || last === null) return 0;
        return current - last;
      };

      return {
        totalPRs: {
          value: String(summary.data.totalPr.current ?? 0),
          diff: calculateDiff(summary.data.totalPr.current ?? 0, summary.data.totalPr.last ?? 0),
          trend: calculateTrend(summary.data.totalPr.current ?? 0, summary.data.totalPr.last ?? 0),
        },
        avgScore: {
          value: String(summary.data.avgQuantityScore.current ?? 0),
          diff:
            (summary.data.avgQuantityScore.current ?? 0) !== undefined &&
            (summary.data.avgQuantityScore.last ?? 0) !== undefined
              ? Number(
                  ((summary.data.avgQuantityScore.current ?? 0) - (summary.data.avgQuantityScore.last ?? 0)).toFixed(1)
                )
              : 0,
          trend: calculateTrend(summary.data.avgQuantityScore.current ?? 0, summary.data.avgQuantityScore.last ?? 0),
        },
        criticalIssues: {
          value: String(summary.data.critical.current ?? 0),
          diff: calculateDiff(summary.data.critical.current ?? 0, summary.data.critical.last ?? 0),
          trend: calculateTrend(summary.data.critical.current ?? 0, summary.data.critical.last ?? 0),
        },
        totalDevelopers: {
          value: String(summary.data.developers.current ?? 0),
          diff: calculateDiff(summary.data.developers.current ?? 0, summary.data.developers.last ?? 0),
          trend: calculateTrend(summary.data.developers.current ?? 0, summary.data.developers.last ?? 0),
        },
      };
    }
    return {
      totalPRs: { value: '307', diff: 12, trend: 'up' as const },
      avgScore: { value: '8.7', diff: 0.2, trend: 'up' as const },
      criticalIssues: { value: '12', diff: -5, trend: 'down' as const },
      totalDevelopers: { value: '5', diff: 1, trend: 'up' as const },
    };
  }, [summary]);

  return (
    <>
      <Stack direction="row" spacing={2} sx={{ mb: 2, justifyContent: 'flex-start' }}>
        <Select size="small" value={view} onChange={(e) => setView(e.target.value as 'month' | 'week')}>
          <MenuItem value="week">過去7日間</MenuItem>
          <MenuItem value="month">過去30日間</MenuItem>
        </Select>
      </Stack>
      <Grid container spacing={3}>
        <Grid xl={3} lg={6} xs={12}>
          {summaryLoading ? (
            <Skeleton variant="rectangular" height={120} sx={{ borderRadius: 3 }} />
          ) : (
            <TotalPRs
              sx={{ height: '100%' }}
              value={overviewData.totalPRs.value}
              diff={overviewData.totalPRs.diff}
              trend={overviewData.totalPRs.trend}
            />
          )}
        </Grid>
        <Grid xl={3} lg={6} xs={12}>
          {summaryLoading ? (
            <Skeleton variant="rectangular" height={120} sx={{ borderRadius: 3 }} />
          ) : (
            <AvgQualityScore
              sx={{ height: '100%' }}
              value={overviewData.avgScore.value}
              diff={overviewData.avgScore.diff}
              trend={overviewData.avgScore.trend}
            />
          )}
        </Grid>
        <Grid xl={3} lg={6} xs={12}>
          {summaryLoading ? (
            <Skeleton variant="rectangular" height={120} sx={{ borderRadius: 3 }} />
          ) : (
            <CriticalIssues
              sx={{ height: '100%' }}
              value={overviewData.criticalIssues.value}
              diff={overviewData.criticalIssues.diff}
              trend={overviewData.criticalIssues.trend}
            />
          )}
        </Grid>
        <Grid xl={3} lg={6} xs={12}>
          {summaryLoading ? (
            <Skeleton variant="rectangular" height={120} sx={{ borderRadius: 3 }} />
          ) : (
            <TotalDevelopers
              sx={{ height: '100%' }}
              value={overviewData.totalDevelopers.value}
              diff={overviewData.totalDevelopers.diff}
              trend={overviewData.totalDevelopers.trend}
            />
          )}
        </Grid>

        <Grid lg={8} xs={12}>
          {trendLoading ? (
            <Skeleton variant="rectangular" height={400} sx={{ borderRadius: 3 }} />
          ) : (
            <CodeQualityCharts data={codeQualityTrend?.data ?? []} sx={{ height: '100%' }} view={view} />
          )}
        </Grid>
        <Grid lg={4} xs={12}>
          {topDevsLoading ? (
            <Skeleton variant="rectangular" height={400} sx={{ borderRadius: 3 }} />
          ) : (
            <TopDevelopers data={topDevelopers?.data ?? []} sx={{ height: '100%' }} view={view} />
          )}
        </Grid>
        <Grid lg={6} xs={12}>
          {commentCatLoading ? (
            <Skeleton variant="rectangular" height={400} sx={{ borderRadius: 3 }} />
          ) : (
            <BugTypeChart data={commentCategoryCounts?.data ?? []} sx={{ height: '100%' }} view={view} />
          )}
        </Grid>
        <Grid lg={6} xs={12}>
          {commentCatLoading ? (
            <Skeleton variant="rectangular" height={400} sx={{ borderRadius: 3 }} />
          ) : (
            <StageChart
              sx={{ height: '100%' }}
              view={view}
              categories={(() => {
                const filtered = commentStageCounts?.data?.filter(
                  (item) => item && item.commentStage != null
                ) ?? [];
                return filtered.map((item) => item.commentStage as string);
              })()}
              data={(() => {
                const filtered = commentStageCounts?.data?.filter(
                  (item) => item && item.commentStage != null
                ) ?? [];
                return filtered.map((item) => item.count ?? 0);
              })()}
              loading={commentStageLoading}
            />
          )}
        </Grid>
      </Grid>
    </>
  );
}
