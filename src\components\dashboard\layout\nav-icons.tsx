import type { Icon } from '@phosphor-icons/react/dist/lib/types';
import { Building, House } from '@phosphor-icons/react/dist/ssr';
import { ChartPie as ChartPieIcon } from '@phosphor-icons/react/dist/ssr/ChartPie';
import { GitPullRequest as GitPullRequestIcon } from '@phosphor-icons/react/dist/ssr/GitPullRequest';

export const navIcons = {
  'chart-pie': ChartPieIcon,
  'PRs': GitPullRequestIcon,
  'testResult': House,
  'issueStatus': Building,
} as Record<string, Icon>;
