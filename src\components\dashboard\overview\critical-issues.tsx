import * as React from 'react';
import type { SxProps } from '@mui/material/styles';
import { BugBeetle as BugBeetleIcon } from '@phosphor-icons/react/dist/ssr/BugBeetle';

import { OverviewCard } from './overview-card';

export interface CriticalIssuesProps {
  sx?: SxProps;
  value: string;
  view?: 'month' | 'week';
  diff?: number;
  trend?: 'up' | 'down' | undefined;
}

export function CriticalIssues({ sx, value, view = 'month', diff, trend }: CriticalIssuesProps): React.JSX.Element {
  return (
    <OverviewCard
      sx={sx}
      value={value}
      label="重大な問題数"
      icon={<BugBeetleIcon fontSize="var(--icon-fontSize-lg)" />}
      avatarColor="var(--mui-palette-error-main)"
      diff={diff}
      trend={trend}
      view={view}
    />
  );
}
