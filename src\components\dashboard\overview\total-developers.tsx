import * as React from 'react';
import type { SxProps } from '@mui/material/styles';
import { Users as UsersIcon } from '@phosphor-icons/react/dist/ssr/Users';

import { OverviewCard } from './overview-card';

export interface TotalDevelopersProps {
  sx?: SxProps;
  value: string;
  view?: 'month' | 'week';
  diff?: number;
  trend?: 'up' | 'down';
}

export function TotalDevelopers({ sx, value, view = 'month', diff, trend }: TotalDevelopersProps): React.JSX.Element {
  return (
    <OverviewCard
      sx={sx}
      value={value}
      label="開発者数"
      icon={<UsersIcon fontSize="var(--icon-fontSize-lg)" />}
      avatarColor="var(--mui-palette-warning-main)"
      diff={diff}
      trend={trend}
      view={view}
    />
  );
}
