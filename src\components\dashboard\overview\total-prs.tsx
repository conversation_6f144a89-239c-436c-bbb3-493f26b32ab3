import * as React from 'react';
import type { SxProps } from '@mui/material/styles';
import { GitPullRequest as GitPullRequestIcon } from '@phosphor-icons/react/dist/ssr/GitPullRequest';

import { OverviewCard } from './overview-card';

export interface TotalPRsProps {
  diff?: number;
  trend?: 'up' | 'down';
  sx?: SxProps;
  value: string;
  view?: 'month' | 'week';
}

export function TotalPRs({ diff, trend, sx, value, view = 'month' }: TotalPRsProps): React.JSX.Element {
  const displayTrendValue = view === 'week' ? 1 : diff;
  return (
    <OverviewCard
      sx={sx}
      value={value}
      label="プルリクエスト総数"
      icon={<GitPullRequestIcon fontSize="var(--icon-fontSize-lg)" />}
      avatarColor="var(--mui-palette-primary-main)"
      diff={displayTrendValue}
      trend={trend}
      view={view}
    />
  );
}
