import * as React from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Chip,
  Divider,
  Grid,
  Paper,
  Stack,
  Table,
  TableBody,
  TableCell,
  tableCellClasses,
  TableContainer,
  TableHead,
  TableRow,
  ToggleButton,
  ToggleButtonGroup,
  Typography,
} from '@mui/material';
import { styled } from '@mui/material/styles';

// --- Mock data derived from the provided screenshot ---
const mockSnapshots = {
  today: {
    header: { totalUnresolved: 0 },
    overview: {
      resolvedRatePct: 100.0,
      unresolvedRatePct: 0.0,
      pendingVerificationPct: 0.0,
      inProgressPct: null,
      newPct: null,
    },
    totals: { cumulative: 101, completed: 101 },
    severities: [
      { key: 'critical', label: 'クリティカル', cumulative: 2, completed: 2 },
      { key: 'major', label: '重大', cumulative: 71, completed: 71 },
      { key: 'minor', label: '軽微', cumulative: 28, completed: 28 },
      { key: 'trivial', label: '些細', cumulative: 0, completed: 0 },
    ],
    unresolvedBreakdown: {
      solved: 0, // 解決済み
      new: 0, // 新規
      assigned: 0, // 割り当て済み
      inProgress: 0, // 進行中
    },
  },
  lastWeek: {
    header: { totalUnresolved: 6 },
    overview: {
      resolvedRatePct: 92.1,
      unresolvedRatePct: 7.9,
      pendingVerificationPct: 1.5,
      inProgressPct: 3.2,
      newPct: 3.2,
    },
    totals: { cumulative: 126, completed: 116 },
    severities: [
      { key: 'critical', label: 'クリティカル', cumulative: 3, completed: 2 },
      { key: 'major', label: '重大', cumulative: 89, completed: 83 },
      { key: 'minor', label: '軽微', cumulative: 32, completed: 31 },
      { key: 'trivial', label: '些細', cumulative: 2, completed: 0 },
    ],
    unresolvedBreakdown: {
      solved: 0,
      new: 2,
      assigned: 2,
      inProgress: 2,
    },
  },
};

const HeadCell = styled(TableCell)(({ theme }) => ({
  [`&.${tableCellClasses.head}`]: {
    backgroundColor: theme.palette.grey[100],
    borderRight: `1px solid ${theme.palette.divider}`,
    textAlign: 'center',
    fontWeight: 700,
  },
}));

const SectionHeader = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: 8,
}));

const color = {
  blue: '#b9d4ef',
  red: '#f6b8b8',
  purple: '#d7c6ff',
  green: '#d4f3d6',
  orange: '#ffe0b2',
  headerRed: '#e10600',
};

const Pill = ({ label, value, tone }: any) => (
  <Card elevation={0} sx={{ borderRadius: 2, background: tone, px: 2, py: 1 }}>
    <Typography sx={{ fontWeight: 700, textAlign: 'center' }}>{label}</Typography>
    <Typography variant="h5" sx={{ fontWeight: 800, textAlign: 'center' }}>
      {value}
    </Typography>
  </Card>
);

function UnresolvedHeader({ total }: { total: number }) {
  return (
    <Box
      sx={{
        bgcolor: color.headerRed,
        color: '#fff',
        px: 2,
        py: 1,
        borderRadius: 1.5,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
      }}
    >
      <Typography sx={{ fontWeight: 800 }}>未解決欠陥</Typography>
      <Typography variant="h5" sx={{ fontWeight: 900 }}>
        {total}
      </Typography>
    </Box>
  );
}

export default function MUIDefectStatusDashboard() {
  const [view, setView] = React.useState<'today' | 'lastWeek'>('today');
  const data = mockSnapshots[view];

  return (
    <Box sx={{ p: 3, bgcolor: (t) => t.palette.background.default }}>
      <Grid container spacing={2} alignItems="center">
        <Grid item xs={12} md={8}>
          <Typography variant="h5" sx={{ fontWeight: 800 }}>
            欠陥ダッシュボード
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Material UI (MUI) – Mockup based on the provided table.
          </Typography>
        </Grid>
        <Grid item xs={12} md={4}>
          <Stack direction="row" justifyContent={{ xs: 'flex-start', md: 'flex-end' }}>
            <ToggleButtonGroup exclusive size="small" value={view} onChange={(_, v) => v && setView(v)} color="primary">
              <ToggleButton value="today">Today</ToggleButton>
              <ToggleButton value="lastWeek">Last week</ToggleButton>
            </ToggleButtonGroup>
          </Stack>
        </Grid>
      </Grid>

      <Divider sx={{ my: 2 }} />

      <Grid container spacing={2}>
        {/* Left column – Overview cards */}
        <Grid item xs={12} md={3}>
          <Stack spacing={1.5}>
            <Pill label="解決率(%)" value={`${data.overview.resolvedRatePct.toFixed(1)}%`} tone={color.blue} />
            <Pill label="未解決(%)" value={`${data.overview.unresolvedRatePct.toFixed(1)}%`} tone={color.red} />
            <Stack direction="row" spacing={1.5}>
              <Box sx={{ flex: 1 }}>
                <Pill
                  label="検証待機(%)"
                  value={`${(data.overview.pendingVerificationPct ?? 0).toFixed(1)}%`}
                  tone={color.purple}
                />
              </Box>
              <Box sx={{ flex: 1 }}>
                <Pill
                  label="進行中(%)"
                  value={data.overview.inProgressPct == null ? '-' : `${data.overview.inProgressPct.toFixed(1)}%`}
                  tone={color.orange}
                />
              </Box>
            </Stack>
            <Pill
              label="新規(%)"
              value={data.overview.newPct == null ? '-' : `${data.overview.newPct.toFixed(1)}%`}
              tone={color.green}
            />
          </Stack>
        </Grid>

        {/* Middle table – Severity & Totals */}
        <Grid item xs={12} md={6}>
          <TableContainer
            component={Paper}
            elevation={0}
            sx={{ borderRadius: 2, border: (t) => `1px solid ${t.palette.divider}` }}
          >
            <Table size="small" aria-label="severity-table">
              <TableHead>
                <TableRow>
                  <HeadCell rowSpan={2} sx={{ width: 160 }}>
                    欠陥 の深刻度
                  </HeadCell>
                  <HeadCell colSpan={2}>合計</HeadCell>
                  <HeadCell colSpan={2}>ステータス</HeadCell>
                </TableRow>
                <TableRow>
                  <HeadCell>累積欠陥</HeadCell>
                  <HeadCell>完了</HeadCell>
                  <HeadCell>未解決</HeadCell>
                  <HeadCell>解決率(%)</HeadCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {/* Totals row */}
                <TableRow>
                  <TableCell sx={{ fontWeight: 700, bgcolor: '#fafafa' }}>合計</TableCell>
                  <TableCell align="right" sx={{ fontWeight: 700 }}>
                    {data.totals.cumulative}
                  </TableCell>
                  <TableCell align="right" sx={{ fontWeight: 700 }}>
                    {data.totals.completed}
                  </TableCell>
                  <TableCell align="right" sx={{ fontWeight: 700 }}>
                    {Math.max(data.totals.cumulative - data.totals.completed, 0)}
                  </TableCell>
                  <TableCell align="right" sx={{ fontWeight: 700 }}>
                    {((data.totals.completed / Math.max(data.totals.cumulative || 1, 1)) * 100).toFixed(1)}%
                  </TableCell>
                </TableRow>
                {data.severities.map((s) => {
                  const unresolved = Math.max(s.cumulative - s.completed, 0);
                  const rate = ((s.completed / Math.max(s.cumulative || 1, 1)) * 100).toFixed(1);
                  return (
                    <TableRow key={s.key}>
                      <TableCell>{s.label}</TableCell>
                      <TableCell align="right">{s.cumulative}</TableCell>
                      <TableCell align="right">{s.completed}</TableCell>
                      <TableCell align="right">{unresolved}</TableCell>
                      <TableCell align="right">{rate}%</TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </TableContainer>
        </Grid>

        {/* Right column – Unresolved breakdown */}
        <Grid item xs={12} md={3}>
          <Stack spacing={1.5}>
            <UnresolvedHeader total={data.header.totalUnresolved} />
            <Card elevation={0} sx={{ borderRadius: 2 }}>
              <CardContent>
                <Stack spacing={1.25}>
                  <Row label="解決済み" value={data.unresolvedBreakdown.solved} />
                  <Row label="新規" value={data.unresolvedBreakdown.new} />
                  <Row label="割り当て済み" value={data.unresolvedBreakdown.assigned} />
                  <Row label="進行中" value={data.unresolvedBreakdown.inProgress} />
                </Stack>
              </CardContent>
            </Card>
          </Stack>
        </Grid>
      </Grid>
    </Box>
  );
}

function Row({ label, value }: { label: string; value: number }) {
  return (
    <Stack direction="row" alignItems="center" justifyContent="space-between">
      <Typography>{label}</Typography>
      <Chip label={value} size="small" />
    </Stack>
  );
}
