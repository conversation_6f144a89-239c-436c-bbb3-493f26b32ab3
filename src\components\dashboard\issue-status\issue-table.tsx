import * as React from 'react';
import { Box, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow } from '@mui/material';
import { styled } from '@mui/material/styles';

// Styled components for the table
const StyledTableContainer = styled(TableContainer)(() => ({
  border: '2px solid #666',
  borderRadius: 0,
}));

const HeaderCell = styled(TableCell)(() => ({
  backgroundColor: '#b0b0b0',
  border: '1px solid #666',
  fontWeight: 'bold',
  textAlign: 'center',
  padding: '8px',
  fontSize: '14px',
}));

const StatusHeaderCell = styled(TableCell)(() => ({
  backgroundColor: '#b0b0b0',
  border: '1px solid #666',
  fontWeight: 'bold',
  textAlign: 'center',
  padding: '4px',
  fontSize: '12px',
}));

const ResolvedCell = styled(TableCell)(() => ({
  backgroundColor: '#a8d4f0',
  border: '1px solid #666',
  textAlign: 'center',
  padding: '8px',
  fontWeight: 'bold',
  fontSize: '16px',
}));

const UnresolvedCell = styled(TableCell)(() => ({
  backgroundColor: '#f0a8a8',
  border: '1px solid #666',
  textAlign: 'center',
  padding: '8px',
  fontWeight: 'bold',
  fontSize: '16px',
}));

const ProgressCell = styled(TableCell)(() => ({
  backgroundColor: '#d4a8f0',
  border: '1px solid #666',
  textAlign: 'center',
  padding: '8px',
  fontWeight: 'bold',
  fontSize: '14px',
}));

const NewCell = styled(TableCell)(() => ({
  backgroundColor: '#a8f0a8',
  border: '1px solid #666',
  textAlign: 'center',
  padding: '8px',
  fontWeight: 'bold',
  fontSize: '14px',
}));

const DataCell = styled(TableCell)(() => ({
  border: '1px solid #666',
  textAlign: 'center',
  padding: '8px',
  backgroundColor: '#fff',
}));

const UnresolvedRedCell = styled(TableCell)(() => ({
  backgroundColor: '#ff4444',
  border: '1px solid #666',
  textAlign: 'center',
  padding: '8px',
  fontWeight: 'bold',
  color: 'white',
  fontSize: '16px',
}));

// Mock data for the defect analysis table
const mockData = [
  {
    severity: '合計',
    total: 101,
    completed: 101,
    resolved: 0,
    new: 0,
    assigned: 0,
    inProgress: 0,
  },
  {
    severity: 'クリティカル',
    total: 2,
    completed: 2,
    resolved: 0,
    new: 0,
    assigned: 0,
    inProgress: 0,
  },
  {
    severity: '重大',
    total: 71,
    completed: 71,
    resolved: 0,
    new: 0,
    assigned: 0,
    inProgress: 0,
  },
  {
    severity: '軽微',
    total: 28,
    completed: 28,
    resolved: 0,
    new: 0,
    assigned: 0,
    inProgress: 0,
  },
  {
    severity: '些細',
    total: 0,
    completed: 0,
    resolved: 0,
    new: 0,
    assigned: 0,
    inProgress: 0,
  },
];

export function IssueTable(): React.JSX.Element {
  return (
    <Box sx={{ padding: 2 }}>
      <Paper>
        <StyledTableContainer>
          <Table size="small">
            <TableHead>
              <TableRow>
                <HeaderCell rowSpan={3} sx={{ width: '120px', verticalAlign: 'middle' }}>
                  欠陥状況
                </HeaderCell>
                <HeaderCell rowSpan={2} sx={{ width: '100px', verticalAlign: 'middle' }}>
                  欠陥の深刻度
                </HeaderCell>
                <HeaderCell colSpan={5}>欠陥状態</HeaderCell>
              </TableRow>
              <TableRow>
                <StatusHeaderCell sx={{ width: '80px' }}>累積欠陥</StatusHeaderCell>
                <StatusHeaderCell sx={{ width: '60px' }}>完了</StatusHeaderCell>
                <StatusHeaderCell colSpan={3} sx={{ backgroundColor: '#b0b0b0' }}>
                  未解決欠陥
                </StatusHeaderCell>
              </TableRow>
              <TableRow>
                <StatusHeaderCell></StatusHeaderCell>
                <StatusHeaderCell></StatusHeaderCell>
                <StatusHeaderCell sx={{ width: '60px' }}>解決済み</StatusHeaderCell>
                <StatusHeaderCell sx={{ width: '60px' }}>新規</StatusHeaderCell>
                <StatusHeaderCell sx={{ width: '60px' }}>割り当て済み</StatusHeaderCell>
                <StatusHeaderCell sx={{ width: '60px' }}>進行中</StatusHeaderCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {/* Resolution percentage row */}
              <TableRow>
                <ResolvedCell rowSpan={2}>解決率(%)</ResolvedCell>
                <UnresolvedCell rowSpan={2}>未解決(%)</UnresolvedCell>
                <DataCell>{mockData[0].total}</DataCell>
                <DataCell>{mockData[0].completed}</DataCell>
                <DataCell>{mockData[0].resolved}</DataCell>
                <DataCell>{mockData[0].new}</DataCell>
                <DataCell>{mockData[0].assigned}</DataCell>
                <UnresolvedRedCell>{mockData[0].inProgress}</UnresolvedRedCell>
              </TableRow>
              <TableRow>
                <DataCell sx={{ fontSize: '20px', fontWeight: 'bold', color: '#0066cc' }}>100.0%</DataCell>
                <DataCell sx={{ fontSize: '20px', fontWeight: 'bold', color: '#cc0000' }}>0.0%</DataCell>
                <DataCell>{mockData[1].resolved}</DataCell>
                <DataCell>{mockData[1].new}</DataCell>
                <DataCell>{mockData[1].assigned}</DataCell>
                <DataCell>{mockData[1].inProgress}</DataCell>
              </TableRow>

              {/* Status percentage row */}
              <TableRow>
                <ProgressCell rowSpan={2}>検証待機(%)</ProgressCell>
                <StatusHeaderCell sx={{ backgroundColor: '#d4a8a8' }}>進行中(%)</StatusHeaderCell>
                <NewCell rowSpan={2}>新規(%)</NewCell>
                <DataCell>{mockData[2].total}</DataCell>
                <DataCell>{mockData[2].completed}</DataCell>
                <DataCell>{mockData[2].resolved}</DataCell>
                <DataCell>{mockData[2].new}</DataCell>
                <DataCell>{mockData[2].assigned}</DataCell>
              </TableRow>
              <TableRow>
                <StatusHeaderCell sx={{ backgroundColor: '#d4a8a8' }}>-</StatusHeaderCell>
                <DataCell>{mockData[3].total}</DataCell>
                <DataCell>{mockData[3].completed}</DataCell>
                <DataCell>{mockData[3].resolved}</DataCell>
                <DataCell>{mockData[3].new}</DataCell>
                <DataCell>{mockData[3].assigned}</DataCell>
              </TableRow>
              <TableRow>
                <DataCell sx={{ fontSize: '16px', fontWeight: 'bold' }}>0.0%</DataCell>
                <DataCell sx={{ fontSize: '16px', fontWeight: 'bold' }}>-</DataCell>
                <DataCell sx={{ fontSize: '16px', fontWeight: 'bold' }}>-</DataCell>
                <DataCell>{mockData[4].total}</DataCell>
                <DataCell>{mockData[4].completed}</DataCell>
                <DataCell>{mockData[4].resolved}</DataCell>
                <DataCell>{mockData[4].new}</DataCell>
                <DataCell>{mockData[4].assigned}</DataCell>
              </TableRow>
            </TableBody>
          </Table>
        </StyledTableContainer>
      </Paper>
    </Box>
  );
}
